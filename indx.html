<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة الموظفين - شركة فور جي لتوصيل الطلبات</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام متكامل لإدارة الموظفين والسائقين والرواتب وأسطول المركبات - شركة فور جي">
  <meta name="keywords" content="موظفين, سائقين, رواتب, توصيل طلبات, فور جي, الكويت">
  <meta name="author" content="MOHAMED T">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #334155 100%);
      min-height: 100vh;
    }
    .glass-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
    }
    .pro-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .pro-card:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.5);
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 30px 60px rgba(59, 130, 246, 0.2);
    }
    .pro-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    .pro-btn:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
    }
    .pro-btn-outline {
      background: transparent;
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      transition: all 0.3s ease;
    }
    .pro-btn-outline:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.8);
      color: #3b82f6;
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }
    .animate-scale-in {
      animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    @keyframes scaleIn {
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
    .floating-animation {
      animation: floating 6s ease-in-out infinite;
    }
    @keyframes floating {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    .gradient-text {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    .stats-card {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;
    }
    .stats-card:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    .notification {
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      z-index: 1050;
      transform: translateY(-120px);
      transition: transform 0.4s ease-in-out;
    }
    .notification.show {
      transform: translateY(0);
    }
    .modal {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(10px);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
    }
    .modal.show {
        opacity: 1;
        visibility: visible;
    }
    .modal-content {
      background: rgba(15, 23, 42, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-height: 90vh;
      overflow-y: auto;
      width: 100%;
    }
    .form-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      transition: all 0.3s ease;
    }
    .form-input:focus {
      background: rgba(255, 255, 255, 0.15);
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 600; }
    .status-active { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }
    .status-on_leave { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid rgba(245, 158, 11, 0.3); }
    .status-inactive { background: rgba(239, 68, 68, 0.2); color: #ef4444; border: 1px solid rgba(239, 68, 68, 0.3); }
    .category-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 600; }
    .job-driver { background: rgba(59, 130, 246, 0.2); color: #3b82f6; border: 1px solid rgba(59, 130, 246, 0.3); }
    .job-supervisor { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid rgba(245, 158, 11, 0.3); }
    .job-admin { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }
    
    /* Print styles for Payslip */
    @media print {
      body * { visibility: hidden; }
      #payslipModalContent, #payslipModalContent * { visibility: visible; }
      #payslipModalContent {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 20px;
        border: none;
        box-shadow: none;
        background: white !important;
        color: black !important;
      }
      #payslipModalContent h3, #payslipModalContent h4, #payslipModalContent p, #payslipModalContent span, #payslipModalContent td {
        color: black !important;
      }
      #printPayslipBtn, #closePayslipBtn { display: none; }
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- Header -->
  <header class="glass-card rounded-none border-x-0 border-t-0 mb-8">
    <div class="container mx-auto px-6 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center floating-animation">
            <i class="fas fa-shipping-fast text-white text-2xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold gradient-text">نظام إدارة الموظفين المتكامل</h1>
            <p class="text-blue-200 text-lg">شركة فور جي لتوصيل الطلبات</p>
          </div>
        </div>
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-center">
            <div class="text-sm text-blue-200">التاريخ الحالي</div>
            <div id="currentDate" class="text-white font-semibold"></div>
          </div>
          <div class="text-center">
            <div class="text-sm text-blue-200">الوقت الحالي</div>
            <div id="currentTime" class="text-white font-semibold"></div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="container mx-auto px-6">
    <!-- Welcome Section -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in">
      <div class="mb-6">
        <h2 class="text-4xl font-bold text-white mb-4">أهلاً بك في نظام فور جي للموظفين</h2>
        <p class="text-xl text-slate-300 leading-relaxed">نظام شامل لإدارة شؤون الموظفين، الرواتب، أسطول المركبات، وعمليات التوصيل</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
        <div class="stats-card rounded-2xl p-6"><div class="text-3xl font-bold text-blue-400 mb-2" id="totalEmployees">0</div><div class="text-slate-300">إجمالي الموظفين</div></div>
        <div class="stats-card rounded-2xl p-6"><div class="text-3xl font-bold text-green-400 mb-2" id="onDutyDrivers">0</div><div class="text-slate-300">سائقين بالخدمة</div></div>
        <div class="stats-card rounded-2xl p-6"><div class="text-3xl font-bold text-purple-400 mb-2" id="totalVehicles">0</div><div class="text-slate-300">إجمالي المركبات</div></div>
        <div class="stats-card rounded-2xl p-6"><div class="text-3xl font-bold text-yellow-400 mb-2" id="monthlyDeductions">0</div><div class="text-slate-300">خصومات الشهر (د.ك)</div></div>
      </div>
    </div>

    <!-- Main Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.1s">
        <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-users-cog text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة الموظفين</h3><p class="text-slate-300 mb-8 leading-relaxed">إضافة وتعديل بيانات الموظفين وتسجيل أجور طلبات الشركات لهم</p><button class="pro-btn pro-btn-outline w-full" onclick="showEmployeesSection()"><i class="fas fa-users"></i> إدارة الموظفين</button>
      </div>
       <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.2s">
        <div class="w-20 h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-calendar-times text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4">الإجازات والخصومات</h3><p class="text-slate-300 mb-8 leading-relaxed">إدارة إجازات الموظفين وتسجيل الخصومات المالية عليهم</p><button class="pro-btn pro-btn-outline w-full" onclick="showLeavesDeductionsSection()"><i class="fas fa-calendar-times"></i> فتح القسم</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.5s">
        <div class="w-20 h-20 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-folder-open text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4">مستندات الموظفين</h3><p class="text-slate-300 mb-8 leading-relaxed">إدارة وأرشفة المستندات الرسمية للموظفين (هويات، رخص قيادة، عقود عمل)</p><button class="pro-btn pro-btn-outline w-full" onclick="showDocumentsEmployeeList()"><i class="fas fa-folder-open"></i> إدارة المستندات</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.3s">
        <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-truck text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة أسطول المركبات</h3><p class="text-slate-300 mb-8 leading-relaxed">متابعة حالة المركبات، الصيانة الدورية، وتعيين المركبات للسائقين</p><button class="pro-btn pro-btn-outline w-full" onclick="showFleetSection()"><i class="fas fa-truck"></i> إدارة الأسطول</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.6s">
        <div class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-chart-line text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4">التقارير وكشوف الرواتب</h3><p class="text-slate-300 mb-8 leading-relaxed">عرض تقارير مفصلة وطباعة كشوف رواتب الموظفين الشهرية</p><button class="pro-btn pro-btn-outline w-full" onclick="showReportsSection()"><i class="fas fa-chart-bar"></i> عرض التقارير</button>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in" style="animation-delay: 0.8s">
      <h3 class="text-2xl font-bold text-white mb-6">معلومات الاتصال</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h4 class="text-lg font-semibold text-blue-300 mb-4">شركة فور جي لتوصيل الطلبات</h4>
          <div class="space-y-3 text-slate-300">
            <div class="flex items-center justify-center gap-3"><i class="fas fa-phone text-green-400"></i><span>+965 57555554</span></div>
            <div class="flex items-center justify-center gap-3"><i class="fas fa-envelope text-blue-400"></i><span><EMAIL> (Placeholder)</span></div>
            <div class="flex items-center justify-center gap-3"><i class="fas fa-map-marker-alt text-red-400"></i><span>مدينة الكويت - شرق - قطعة 5 (Placeholder)</span></div>
          </div>
        </div>
        <div>
          <h4 class="text-lg font-semibold text-purple-300 mb-4">تطوير وتنفيذ</h4>
          <div class="space-y-3 text-slate-300"><div class="flex items-center justify-center gap-3"><i class="fas fa-laptop-code text-purple-400"></i><span class="font-semibold">MOHAMED T</span></div></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="glass-card rounded-none border-x-0 border-b-0 mt-16">
    <div class="container mx-auto px-6 py-8">
      <div class="text-center">
        <p class="text-blue-200 text-lg mb-2">© 2025 شركة فور جي لتوصيل الطلبات - جميع الحقوق محفوظة</p>
        <p class="text-blue-300"><i class="fas fa-code mr-2"></i>تطوير: <span class="font-bold text-white">MOHAMED T</span></p>
      </div>
    </div>
  </footer>

  <!-- Main Modal -->
  <div id="mainModal" class="modal"><div id="mainModalContent" class="modal-content rounded-3xl max-w-6xl animate-scale-in"></div></div>

  <!-- Confirmation Modal -->
  <div id="confirmModal" class="modal">
    <div class="modal-content rounded-3xl max-w-sm animate-scale-in p-8 text-center">
      <h3 id="confirmTitle" class="text-xl font-bold text-white mb-4"></h3>
      <p id="confirmText" class="text-slate-300 mb-6"></p>
      <div class="flex justify-center gap-4">
        <button id="confirmBtn" class="pro-btn bg-red-600 hover:bg-red-700 px-6 py-2 rounded-lg text-white font-semibold">تأكيد</button>
        <button id="cancelBtn" class="pro-btn-outline px-6 py-2 rounded-lg font-semibold">إلغاء</button>
      </div>
    </div>
  </div>
  
  <!-- Payslip Modal -->
  <div id="payslipModal" class="modal"><div id="payslipModalContent" class="modal-content rounded-3xl max-w-2xl animate-scale-in"></div></div>

  <!-- Notification -->
  <div id="notification" class="notification"><div class="glass-card rounded-xl p-4 max-w-md mx-auto"><div class="flex items-center"><i id="notificationIcon" class="fas fa-info-circle text-blue-400 text-xl mr-3 ml-3"></i><span id="notificationText" class="text-white font-semibold"></span></div></div></div>

  <script>
    // --- DATABASE (LocalStorage) ---
    const DB = {
      getEmployees: () => JSON.parse(localStorage.getItem('employeesData') || '[]'),
      saveEmployees: (employees) => localStorage.setItem('employeesData', JSON.stringify(employees)),
      getDocuments: () => JSON.parse(localStorage.getItem('documentsData') || '{}'),
      saveDocuments: (documents) => localStorage.setItem('documentsData', JSON.stringify(documents)),
      getEmployeeOrders: () => JSON.parse(localStorage.getItem('employeeOrdersData') || '{}'),
      saveEmployeeOrders: (orders) => localStorage.setItem('employeeOrdersData', JSON.stringify(orders)),
      getCompanies: () => JSON.parse(localStorage.getItem('companiesData') || '[]'),
      saveCompanies: (companies) => localStorage.setItem('companiesData', JSON.stringify(companies)),
      getLeaves: () => JSON.parse(localStorage.getItem('leavesData') || '{}'),
      saveLeaves: (leaves) => localStorage.setItem('leavesData', JSON.stringify(leaves)),
      getDeductions: () => JSON.parse(localStorage.getItem('deductionsData') || '{}'),
      saveDeductions: (deductions) => localStorage.setItem('deductionsData', JSON.stringify(deductions)),
    };

    // --- INITIALIZATION ---
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🚀 Initializing Employee Management System...');
      initSampleData();
      updateDateTime();
      updateDashboardStats();
      setInterval(updateDateTime, 1000);
      setInterval(updateDashboardStats, 30000);
      showNotification('أهلاً بك في نظام فور جي لإدارة الموظفين', 'success');
    });

    function initSampleData() {
      if (!localStorage.getItem('employeesData')) {
        const sampleData = [
          { id: 101, name: 'أحمد محمود', job: 'driver', phone: '55123456', status: 'active', vehicle: '7-12345', salary: 350 },
          { id: 102, name: 'علي حسين', job: 'driver', phone: '55234567', status: 'active', vehicle: '7-23456', salary: 350 },
          { id: 201, name: 'فاطمة عبدالله', job: 'supervisor', phone: '55456789', status: 'active', vehicle: 'N/A', salary: 550 },
        ];
        DB.saveEmployees(sampleData);
      }
       if (!localStorage.getItem('documentsData')) {
        const docData = {
          101: [{docId: 1, name: 'Civil_ID.pdf', type: 'id'}]
        };
        DB.saveDocuments(docData);
      }
      if (!localStorage.getItem('companiesData')) {
          const companies = ['Deliveroo', '4G', 'Amazon', 'Speedrago', 'Talabat'];
          DB.saveCompanies(companies);
      }
      if (!localStorage.getItem('leavesData')) {
        const leaves = { 101: [{id: 1, date: new Date().toISOString(), type: 'leave', reason: 'إجازة سنوية', duration: 5}] };
        DB.saveLeaves(leaves);
      }
      if (!localStorage.getItem('deductionsData')) {
        const deductions = { 102: [{id: 1, date: new Date().toISOString(), reason: 'غياب', amount: 20}] };
        DB.saveDeductions(deductions);
      }
    }

    // --- UI & DATE/TIME ---
    function updateDateTime() {
      const now = new Date();
      const dateOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
      const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false };
      document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-KW', dateOptions);
      document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-GB', timeOptions);
    }
    
    // --- DASHBOARD ---
    function updateDashboardStats() {
      const employees = DB.getEmployees();
      const allDeductions = DB.getDeductions();
      let totalDeductions = 0;
      for (const empId in allDeductions) {
          totalDeductions += allDeductions[empId].reduce((sum, d) => sum + d.amount, 0);
      }

      const stats = {
        totalEmployees: employees.length,
        onDutyDrivers: employees.filter(e => e.job === 'driver' && e.status === 'active').length,
        totalVehicles: employees.filter(e => e.vehicle && e.vehicle !== 'N/A').length,
        monthlyDeductions: totalDeductions
      };
      animateNumber('totalEmployees', stats.totalEmployees);
      animateNumber('onDutyDrivers', stats.onDutyDrivers);
      animateNumber('totalVehicles', stats.totalVehicles);
      animateNumber('monthlyDeductions', stats.monthlyDeductions);
    }

    function animateNumber(elementId, end) {
      const element = document.getElementById(elementId);
      if (!element) return;
      const start = parseInt(element.textContent.replace(/,/g, '')) || 0;
      if (start === end) return;
      const duration = 1500;
      const startTime = performance.now();
      
      function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOut);
        element.textContent = current.toLocaleString();
        if (progress < 1) requestAnimationFrame(update);
      }
      requestAnimationFrame(update);
    }

    // --- MODALS & NOTIFICATION ---
    const mainModal = document.getElementById('mainModal');
    const mainModalContent = document.getElementById('mainModalContent');
    const confirmModal = document.getElementById('confirmModal');
    const payslipModal = document.getElementById('payslipModal');
    const payslipModalContent = document.getElementById('payslipModalContent');

    function showModal(content, targetModal = mainModal, targetContent = mainModalContent) {
      targetContent.innerHTML = content;
      targetModal.classList.add('show');
    }

    function hideModal(targetModal = mainModal) {
      targetModal.classList.remove('show');
      if (targetModal.querySelector('.modal-content')) {
        targetModal.querySelector('.modal-content').innerHTML = '';
      }
    }
    
    mainModal.addEventListener('click', (e) => { if (e.target === mainModal) hideModal(mainModal); });
    payslipModal.addEventListener('click', (e) => { if (e.target === payslipModal) hideModal(payslipModal); });

    function showConfirm(title, text, onConfirm) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmText').textContent = text;
        confirmModal.classList.add('show');

        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        const close = () => {
            confirmModal.classList.remove('show');
            confirmBtn.replaceWith(confirmBtn.cloneNode(true));
            cancelBtn.replaceWith(cancelBtn.cloneNode(true));
        };
        
        document.getElementById('confirmBtn').onclick = () => { onConfirm(); close(); };
        document.getElementById('cancelBtn').onclick = close;
    }

    function showNotification(message, type = 'info') {
      const notification = document.getElementById('notification');
      const icon = document.getElementById('notificationIcon');
      const text = document.getElementById('notificationText');
      const config = {
        success: { icon: 'fa-check-circle', color: 'text-green-400' },
        error: { icon: 'fa-times-circle', color: 'text-red-400' },
        info: { icon: 'fa-info-circle', color: 'text-blue-400' }
      };
      const typeConfig = config[type] || config.info;
      icon.className = `fas ${typeConfig.icon} ${typeConfig.color} text-xl mr-3 ml-3`;
      text.textContent = message;
      notification.classList.add('show');
      setTimeout(() => notification.classList.remove('show'), 3000);
    }

    // --- EMPLOYEE MANAGEMENT ---
    function showEmployeesSection(searchTerm = '') {
        const employees = DB.getEmployees().filter(emp => emp.name.toLowerCase().includes(searchTerm.toLowerCase()));
        const jobTitles = { 'driver': 'سائق', 'supervisor': 'مشرف', 'admin': 'إداري' };
        const statusText = { 'active': 'نشط', 'on_leave': 'إجازة', 'inactive': 'غير نشط' };

        let employeeCards = employees.map(emp => `
            <div class="pro-card p-4 rounded-2xl flex flex-col justify-between">
                <div>
                    <div class="flex items-center justify-between mb-3">
                        <span class="category-badge job-${emp.job}">${jobTitles[emp.job]}</span>
                        <span class="status-badge status-${emp.status}">${statusText[emp.status]}</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">${emp.name}</h3>
                    <div class="text-blue-200 text-sm space-y-1">
                        <div class="flex items-center"><i class="fas fa-id-card text-purple-400 w-5 text-center mr-2"></i>${emp.id}</div>
                        <div class="flex items-center"><i class="fas fa-phone text-green-400 w-5 text-center mr-2"></i>${emp.phone}</div>
                        ${emp.job === 'driver' ? `<div class="flex items-center"><i class="fas fa-truck text-blue-400 w-5 text-center mr-2"></i>${emp.vehicle || 'N/A'}</div>` : ''}
                    </div>
                </div>
                <div class="flex gap-2 mt-4">
                    <button onclick="showEmployeeForm(${emp.id})" class="flex-1 pro-btn-outline text-xs py-2 rounded-md"><i class="fas fa-edit"></i> تعديل / إضافة طلب</button>
                    <button onclick="deleteEmployee(${emp.id})" class="flex-1 pro-btn-outline text-xs py-2 rounded-md border-red-500/50 text-red-400 hover:bg-red-500/20 hover:text-red-300"><i class="fas fa-trash"></i> حذف</button>
                </div>
            </div>`).join('');

        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between"><h2 class="text-2xl font-bold text-white">إدارة الموظفين</h2><button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button></div>
                <div class="mt-4 flex gap-4"><input type="text" id="searchInput" onkeyup="showEmployeesSection(this.value)" value="${searchTerm}" placeholder="ابحث عن موظف..." class="w-full form-input rounded-lg px-4 py-2"><button onclick="showEmployeeForm()" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold whitespace-nowrap"><i class="fas fa-plus"></i> إضافة موظف</button></div>
            </div>
            <div class="p-6"><div id="employeeList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">${employeeCards || `<p class="text-slate-400 col-span-full text-center">لا يوجد موظفين لعرضهم.</p>`}</div></div>`;
        showModal(content);
    }
    
    function showEmployeeForm(id = null) {
        const isEditing = id !== null;
        const employee = isEditing ? DB.getEmployees().find(e => e.id === id) : {};
        const title = isEditing ? 'تعديل بيانات موظف' : 'إضافة موظف جديد';
        const companies = DB.getCompanies();
        const companyOptions = companies.map(c => `<option value="${c}">${c}</option>`).join('');

        const allOrders = DB.getEmployeeOrders();
        const employeeOrders = (isEditing ? allOrders[id] : []) || [];
        const recentOrdersList = employeeOrders.slice(-5).reverse().map(order => `
            <li class="flex justify-between p-2 bg-slate-800/50 rounded-md">
                <span>${order.companyName}</span>
                <span class="font-bold text-green-400">${parseFloat(order.wage).toFixed(2)} د.ك</span>
            </li>`).join('');

        const formHtml = `
            <div class="p-6 border-b border-white/20"><h2 class="text-2xl font-bold text-white text-center">${title}</h2></div>
            <div class="p-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
                <form id="employeeForm">
                    <h3 class="text-xl font-bold text-white mb-4">بيانات الموظف</h3>
                    <input type="hidden" name="id" value="${employee.id || ''}">
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div><label class="text-slate-300 mb-2 block">الاسم الكامل</label><input type="text" name="name" value="${employee.name || ''}" class="w-full form-input rounded-lg px-4 py-2" required></div>
                            <div><label class="text-slate-300 mb-2 block">رقم الهاتف</label><input type="tel" name="phone" value="${employee.phone || ''}" class="w-full form-input rounded-lg px-4 py-2" required></div>
                            <div><label class="text-slate-300 mb-2 block">الوظيفة</label><select name="job" class="w-full form-input rounded-lg px-4 py-2" required><option value="driver" ${employee.job === 'driver' ? 'selected' : ''}>سائق</option><option value="supervisor" ${employee.job === 'supervisor' ? 'selected' : ''}>مشرف</option><option value="admin" ${employee.job === 'admin' ? 'selected' : ''}>إداري</option></select></div>
                            <div><label class="text-slate-300 mb-2 block">المركبة (للسائقين)</label><input type="text" name="vehicle" value="${employee.vehicle || ''}" class="w-full form-input rounded-lg px-4 py-2"></div>
                            <div><label class="text-slate-300 mb-2 block">الراتب الأساسي</label><input type="number" name="salary" value="${employee.salary || 0}" class="w-full form-input rounded-lg px-4 py-2" required></div>
                            <div><label class="text-slate-300 mb-2 block">الحالة</label><select name="status" class="w-full form-input rounded-lg px-4 py-2" required><option value="active" ${employee.status === 'active' ? 'selected' : ''}>نشط</option><option value="on_leave" ${employee.status === 'on_leave' ? 'selected' : ''}>إجازة</option><option value="inactive" ${employee.status === 'inactive' ? 'selected' : ''}>غير نشط</option></select></div>
                        </div>
                        <div class="flex justify-end gap-4 pt-4">
                            <button type="button" onclick="showEmployeesSection()" class="pro-btn-outline px-6 py-2 rounded-lg font-semibold">إلغاء</button>
                            <button type="submit" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold">حفظ البيانات</button>
                        </div>
                    </div>
                </form>
                ${isEditing ? `
                <div class="border-t lg:border-t-0 lg:border-r border-slate-700 pt-8 lg:pt-0 lg:pr-8">
                    <h3 class="text-xl font-bold text-white mb-4">تسجيل طلب شركة</h3>
                    <form id="employeeOrderForm" class="space-y-3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
                            <select name="companyName" class="form-input rounded-lg p-2">${companyOptions}</select>
                            <input type="number" step="0.01" name="wage" placeholder="أجر الطلب" class="form-input rounded-lg p-2 text-center">
                        </div>
                        <div class="text-left pt-2">
                            <button type="submit" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold w-full md:w-auto">حفظ الطلب</button>
                        </div>
                    </form>
                    <h4 class="text-lg font-semibold text-white mt-6 mb-3">آخر الطلبات المسجلة</h4>
                    <ul class="space-y-2">${recentOrdersList || '<li class="text-slate-400">لا توجد طلبات مسجلة.</li>'}</ul>
                </div>` : ''}
            </div>`;
        showModal(formHtml);
        document.getElementById('employeeForm').addEventListener('submit', (e) => saveEmployee(e, isEditing));
        if(isEditing) {
            document.getElementById('employeeOrderForm').addEventListener('submit', (e) => saveEmployeeOrder(e, id));
        }
    }

    function saveEmployee(event, isEditing) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const employeeData = Object.fromEntries(formData.entries());
        let employees = DB.getEmployees();
        
        employeeData.salary = parseFloat(employeeData.salary);
        
        if (isEditing) {
            const index = employees.findIndex(e => e.id == employeeData.id);
            employees[index] = { ...employees[index], ...employeeData };
            showNotification('تم تحديث بيانات الموظف بنجاح', 'success');
        } else {
            employeeData.id = new Date().getTime();
            employees.push(employeeData);
            showNotification('تمت إضافة الموظف بنجاح', 'success');
        }
        
        DB.saveEmployees(employees);
        updateDashboardStats();
        if (isEditing) {
            showEmployeeForm(employeeData.id);
        } else {
            showEmployeesSection();
        }
    }

    function saveEmployeeOrder(event, empId) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const companyName = formData.get('companyName');
        const wage = parseFloat(formData.get('wage'));

        if(companyName && wage > 0) {
            let allOrders = DB.getEmployeeOrders();
            if(!allOrders[empId]) {
                allOrders[empId] = [];
            }
            allOrders[empId].push({ date: new Date().toISOString(), companyName, wage });
            DB.saveEmployeeOrders(allOrders);
            showNotification('تم حفظ الطلب للموظف', 'success');
            showEmployeeForm(empId); // Refresh the form view
        } else {
            showNotification('الرجاء إدخال بيانات الطلب بشكل صحيح', 'error');
        }
    }

    function deleteEmployee(id) {
        showConfirm('تأكيد الحذف', 'هل أنت متأكد من رغبتك في حذف هذا الموظف؟ سيتم حذف جميع بياناته ومستنداته.', () => {
            let employees = DB.getEmployees().filter(e => e.id !== id);
            let documents = DB.getDocuments();
            let orders = DB.getEmployeeOrders();
            let leaves = DB.getLeaves();
            let deductions = DB.getDeductions();
            delete documents[id];
            delete orders[id];
            delete leaves[id];
            delete deductions[id];
            DB.saveEmployees(employees);
            DB.saveDocuments(documents);
            DB.saveEmployeeOrders(orders);
            DB.saveLeaves(leaves);
            DB.saveDeductions(deductions);
            showNotification('تم حذف الموظف بنجاح', 'success');
            updateDashboardStats();
            showEmployeesSection();
        });
    }

    // --- LEAVES AND DEDUCTIONS ---
    function showLeavesDeductionsSection(searchTerm = '') {
        const employees = DB.getEmployees().filter(emp => emp.name.toLowerCase().includes(searchTerm.toLowerCase()));
        const employeeButtons = employees.map(emp => `<button onclick="showEmployeeLeaveDeductionModal(${emp.id})" class="pro-card p-4 text-center rounded-lg">${emp.name}</button>`).join('');
        
        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">الإجازات والخصومات</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
                <div class="mt-4">
                    <input type="text" onkeyup="showLeavesDeductionsSection(this.value)" value="${searchTerm}" placeholder="ابحث عن موظف..." class="w-full form-input rounded-lg px-4 py-2">
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    ${employeeButtons || `<p class="text-slate-400 col-span-full text-center">لم يتم العثور على موظفين.</p>`}
                </div>
            </div>`;
        showModal(content);
    }

    function showEmployeeLeaveDeductionModal(empId) {
        const employee = DB.getEmployees().find(e => e.id === empId);
        const leaves = DB.getLeaves()[empId] || [];
        const deductions = DB.getDeductions()[empId] || [];

        const leavesList = leaves.map(l => {
            const typeText = l.type === 'lateness' ? 'تأخير' : 'إجازة';
            const durationText = l.type === 'leave' ? `${l.duration} أيام` : l.duration;
            const icon = l.type === 'lateness' ? 'fa-clock' : 'fa-calendar-day';
            return `<li class="flex justify-between p-2 bg-slate-800/50 rounded-md">
                        <div class="flex items-center gap-3"><i class="fas ${icon} text-orange-400"></i> ${l.reason} (${typeText})</div>
                        <div>${durationText}</div>
                    </li>`;
        }).join('');

        const deductionsList = deductions.map(d => `<li class="flex justify-between p-2 bg-slate-800/50 rounded-md"><div>${d.reason}</div><div class="text-red-400">${d.amount.toFixed(2)} د.ك</div></li>`).join('');

        const content = `
            <div class="p-6 border-b border-white/20"><h2 class="text-2xl font-bold text-white">إجازات وخصومات: ${employee.name}</h2><button onclick="showLeavesDeductionsSection()" class="text-white hover:text-blue-400 text-xl"><i class="fas fa-arrow-right"></i></button></div>
            <div class="p-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-white mb-4">تسجيل إجازة / تأخير</h3>
                    <form id="leaveOrLatenessForm" class="space-y-3">
                        <select name="type" id="entryTypeSelect" class="w-full form-input rounded-lg p-2">
                            <option value="leave">إجازة</option>
                            <option value="lateness">تأخير</option>
                        </select>
                        <input type="text" name="reason" placeholder="السبب" class="w-full form-input rounded-lg p-2" required>
                        <div id="durationInputContainer">
                            <input type="number" name="duration" placeholder="عدد الأيام" class="w-full form-input rounded-lg p-2" required>
                        </div>
                        <button type="submit" class="pro-btn w-full py-2 rounded-lg">حفظ</button>
                    </form>
                    <h4 class="text-lg font-semibold text-white mt-6 mb-3">سجل الإجازات والتأخير</h4>
                    <ul class="space-y-2">${leavesList || '<li>لا توجد إدخالات.</li>'}</ul>
                </div>
                <div class="border-t lg:border-t-0 lg:border-r border-slate-700 pt-8 lg:pt-0 lg:pr-8">
                    <h3 class="text-xl font-bold text-white mb-4">تسجيل خصم</h3>
                    <form id="deductionForm" class="space-y-3">
                        <input type="text" name="reason" placeholder="سبب الخصم" class="w-full form-input rounded-lg p-2" required>
                        <input type="number" step="0.01" name="amount" placeholder="المبلغ" class="w-full form-input rounded-lg p-2" required>
                        <button type="submit" class="pro-btn w-full py-2 rounded-lg">حفظ الخصم</button>
                    </form>
                    <h4 class="text-lg font-semibold text-white mt-6 mb-3">سجل الخصومات</h4>
                    <ul class="space-y-2">${deductionsList || '<li>لا توجد خصومات مسجلة.</li>'}</ul>
                </div>
            </div>`;
        showModal(content);
        
        const entryTypeSelect = document.getElementById('entryTypeSelect');
        const durationContainer = document.getElementById('durationInputContainer');
        
        const updateDurationInput = () => {
            if (entryTypeSelect.value === 'leave') {
                durationContainer.innerHTML = `<input type="number" name="duration" placeholder="عدد الأيام" class="w-full form-input rounded-lg p-2" required>`;
            } else { // lateness
                durationContainer.innerHTML = `<input type="text" name="duration" placeholder="مدة التأخير (مثال: ساعتان)" class="w-full form-input rounded-lg p-2" required>`;
            }
        };
        
        entryTypeSelect.addEventListener('change', updateDurationInput);
        
        document.getElementById('leaveOrLatenessForm').addEventListener('submit', e => saveLeaveOrLateness(e, empId));
        document.getElementById('deductionForm').addEventListener('submit', e => saveDeduction(e, empId));
    }

    function saveLeaveOrLateness(event, empId) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const type = formData.get('type');
        const reason = formData.get('reason');
        const duration = formData.get('duration');

        if (reason && duration) {
            const allLeaves = DB.getLeaves();
            if (!allLeaves[empId]) allLeaves[empId] = [];
            const newEntry = {
                id: new Date().getTime(),
                date: new Date().toISOString(),
                type,
                reason,
                duration
            };
            allLeaves[empId].push(newEntry);
            DB.saveLeaves(allLeaves);
            showNotification('تم تسجيل الإدخال بنجاح', 'success');
            showEmployeeLeaveDeductionModal(empId);
        }
    }

    function saveDeduction(event, empId) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const reason = formData.get('reason');
        const amount = parseFloat(formData.get('amount'));
        if (reason && amount > 0) {
            const allDeductions = DB.getDeductions();
            if (!allDeductions[empId]) allDeductions[empId] = [];
            allDeductions[empId].push({ id: new Date().getTime(), date: new Date().toISOString(), reason, amount });
            DB.saveDeductions(allDeductions);
            showNotification('تم تسجيل الخصم بنجاح', 'success');
            showEmployeeLeaveDeductionModal(empId);
            updateDashboardStats();
        }
    }
    
    function showPayslip(empId) {
        const emp = DB.getEmployees().find(e => e.id === empId);
        if(!emp) return;

        const allDeductions = DB.getDeductions()[empId] || [];
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString();

        const totalDeductions = allDeductions
            .filter(d => d.date >= firstDay && d.date <= lastDay)
            .reduce((sum, d) => sum + d.amount, 0);
        
        const orderWages = (DB.getEmployeeOrders()[emp.id] || []).reduce((sum, o) => sum + o.wage, 0);

        const salary = emp.salary || 0;
        const netSalary = salary + orderWages - totalDeductions;
        const month = new Date().toLocaleString('ar-KW', { month: 'long', year: 'numeric' });

        const payslipContent = `
            <div class="p-8 text-black bg-white">
                <div class="flex justify-between items-center border-b-2 pb-4 mb-4">
                    <div><h3 class="text-3xl font-bold text-blue-600">قسيمة راتب (Payslip)</h3><p>شركة فور جي لتوصيل الطلبات</p></div>
                    <div class="text-left"><p class="font-bold">${month}</p><p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-KW')}</p></div>
                </div>
                <div class="mb-6">
                    <h4 class="font-bold text-lg mb-2">بيانات الموظف</h4>
                    <p><span class="font-semibold w-32 inline-block">اسم الموظف:</span> ${emp.name}</p>
                    <p><span class="font-semibold w-32 inline-block">الرقم الوظيفي:</span> ${emp.id}</p>
                    <p><span class="font-semibold w-32 inline-block">الوظيفة:</span> ${emp.job}</p>
                </div>
                <table class="w-full text-center border">
                    <thead class="bg-gray-200"><tr><th class="p-2 border">البيان</th><th class="p-2 border">المبلغ</th></tr></thead>
                    <tbody>
                        <tr><td class="p-2 border text-right">الراتب الأساسي</td><td class="p-2 border text-green-600 font-semibold">${salary.toFixed(2)} د.ك</td></tr>
                        <tr><td class="p-2 border text-right">أجور الطلبات (${month})</td><td class="p-2 border text-green-600 font-semibold">${orderWages.toFixed(2)} د.ك</td></tr>
                        <tr><td class="p-2 border text-right">إجمالي الخصومات</td><td class="p-2 border text-red-600 font-semibold">(${totalDeductions.toFixed(2)}) د.ك</td></tr>
                        <tr class="bg-gray-200 font-bold text-lg"><td class="p-2 border text-right">صافي الراتب</td><td class="p-2 border">${netSalary.toFixed(2)} د.ك</td></tr>
                    </tbody>
                </table>
                <div class="flex justify-between mt-8">
                    <button id="closePayslipBtn" onclick="hideModal(payslipModal)" class="pro-btn-outline px-6 py-2 rounded-lg font-semibold">إغلاق</button>
                    <button id="printPayslipBtn" onclick="window.print()" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold"><i class="fas fa-print mr-2"></i> طباعة</button>
                </div>
            </div>`;
        showModal(payslipContent, payslipModal, payslipModalContent);
    }

    // --- DOCUMENT MANAGEMENT ---
    function showDocumentsEmployeeList(searchTerm = '') {
        const employees = DB.getEmployees().filter(emp => emp.name.toLowerCase().includes(searchTerm.toLowerCase()));
        const employeeButtons = employees.map(emp => `<button onclick="showEmployeeDocs(${emp.id})" class="pro-card p-4 text-center rounded-lg">${emp.name}</button>`).join('');
        
        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">مستندات الموظفين</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
                <div class="mt-4">
                    <input type="text" id="docSearchInput" onkeyup="showDocumentsEmployeeList(this.value)" value="${searchTerm}" placeholder="ابحث عن موظف..." class="w-full form-input rounded-lg px-4 py-2">
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" id="docEmployeeList">
                    ${employeeButtons || `<p class="text-slate-400 col-span-full text-center">لم يتم العثور على موظفين.</p>`}
                </div>
            </div>`;
        showModal(content);
    }

    function showEmployeeDocs(empId) {
        const employee = DB.getEmployees().find(e => e.id === empId);
        const allDocs = DB.getDocuments();
        const employeeDocs = allDocs[empId] || [];
        const docIcons = { id: 'fa-id-card', passport: 'fa-passport', contract: 'fa-file-contract', other: 'fa-file-alt' };

        const docList = employeeDocs.map(doc => `
            <div class="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                <div class="flex items-center gap-3">
                    <i class="fas ${docIcons[doc.type] || docIcons.other} text-blue-400"></i>
                    <span class="text-white">${doc.name}</span>
                </div>
                <button onclick="deleteDocument(${empId}, ${doc.docId})" class="text-red-400 hover:text-red-300"><i class="fas fa-trash"></i></button>
            </div>`).join('');

        const content = `
            <div class="p-6 border-b border-white/20"><div class="flex items-center justify-between"><h2 class="text-2xl font-bold text-white">مستندات: ${employee.name}</h2><button onclick="showDocumentsEmployeeList()" class="text-white hover:text-blue-400 text-xl"><i class="fas fa-arrow-right"></i></button></div></div>
            <div class="p-6">
                <div class="mb-4">
                    <form id="uploadDocForm" class="flex gap-4">
                        <input type="text" id="docNameInput" placeholder="اسم الملف..." class="w-full form-input rounded-lg px-4 py-2" required>
                        <select id="docTypeInput" class="form-input rounded-lg px-4 py-2">
                            <option value="id">بطاقة مدنية</option><option value="passport">جواز سفر</option><option value="contract">عقد عمل</option><option value="other">أخرى</option>
                        </select>
                        <button type="submit" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold whitespace-nowrap"><i class="fas fa-upload"></i> رفع</button>
                    </form>
                </div>
                <div class="space-y-3">${docList || '<p class="text-slate-400 text-center">لا توجد مستندات لهذا الموظف.</p>'}</div>
            </div>`;
        showModal(content);
        document.getElementById('uploadDocForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const docName = document.getElementById('docNameInput').value;
            const docType = document.getElementById('docTypeInput').value;
            if(docName) uploadDocument(empId, docName, docType);
        });
    }

    function uploadDocument(empId, docName, docType) {
        let allDocs = DB.getDocuments();
        if (!allDocs[empId]) allDocs[empId] = [];
        const newDoc = { docId: new Date().getTime(), name: docName, type: docType };
        allDocs[empId].push(newDoc);
        DB.saveDocuments(allDocs);
        showNotification('تم رفع المستند بنجاح', 'success');
        showEmployeeDocs(empId);
    }
    
    function deleteDocument(empId, docId) {
        showConfirm('تأكيد الحذف', 'هل أنت متأكد من حذف هذا المستند؟', () => {
            let allDocs = DB.getDocuments();
            if (allDocs[empId]) {
                allDocs[empId] = allDocs[empId].filter(doc => doc.docId !== docId);
                DB.saveDocuments(allDocs);
                showNotification('تم حذف المستند', 'success');
                showEmployeeDocs(empId);
            }
        });
    }

    // --- OTHER SECTIONS (STATIC) ---
    function showFleetSection() {
        const content = `
         <div class="p-6 border-b border-white/20"><div class="flex items-center justify-between"><h2 class="text-2xl font-bold text-white">إدارة الأسطول</h2><button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button></div></div>
         <div class="p-8 text-center"><i class="fas fa-truck text-6xl text-purple-400 mb-4"></i><h3 class="text-2xl font-bold text-white mb-4">حالة أسطول المركبات</h3><p class="text-blue-200 mb-6">إجمالي المركبات: 105 مركبة</p><div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6"><div class="stats-card rounded-xl p-4"><div class="text-2xl font-bold text-green-400">88</div><div class="text-slate-300">مركبات عاملة</div></div><div class="stats-card rounded-xl p-4"><div class="text-2xl font-bold text-yellow-400">12</div><div class="text-slate-300">تحت الصيانة</div></div><div class="stats-card rounded-xl p-4"><div class="text-2xl font-bold text-blue-400">5</div><div class="text-slate-300">مركبات متاحة</div></div></div><div class="mt-8"><button class="pro-btn px-6 py-3 rounded-xl"><i class="fas fa-tools mr-2 ml-2"></i>جدولة صيانة</button></div></div>`;
        showModal(content);
    }
    function showReportsSection() {
        const employees = DB.getEmployees();
        const allDeductions = DB.getDeductions();
        const allOrders = DB.getEmployeeOrders();

        const payrollRows = employees.map(emp => {
            const salary = emp.salary || 0;
            const totalDeductions = (allDeductions[emp.id] || []).reduce((sum, d) => sum + d.amount, 0);
            const orderWages = (allOrders[emp.id] || []).reduce((sum, o) => sum + o.wage, 0);
            const netSalary = salary + orderWages - totalDeductions;
            return `
                <tr class="border-b border-slate-700 hover:bg-slate-800/50">
                    <td class="p-3">${emp.name}</td>
                    <td class="p-3">${salary.toFixed(2)}</td>
                    <td class="p-3">${orderWages.toFixed(2)}</td>
                    <td class="p-3 text-red-400">${totalDeductions.toFixed(2)}</td>
                    <td class="p-3 font-bold text-green-400">${netSalary.toFixed(2)}</td>
                    <td class="p-3"><button onclick="showPayslip(${emp.id})" class="pro-btn-outline text-xs py-1 px-3 rounded-md"><i class="fas fa-print"></i></button></td>
                </tr>`;
        }).join('');

        const content = `
         <div class="p-6 border-b border-white/20"><div class="flex items-center justify-between"><h2 class="text-2xl font-bold text-white">التقارير وكشوف الرواتب</h2><button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button></div></div>
         <div class="p-8">
            <h3 class="text-xl font-bold text-white mb-4">كشف الرواتب الشهري</h3>
            <div class="overflow-x-auto">
                <table class="w-full text-white text-center">
                    <thead class="bg-slate-800/50"><tr><th class="p-3">الموظف</th><th class="p-3">الأساسي</th><th class="p-3">أجور الطلبات</th><th class="p-3">الخصومات</th><th class="p-3">صافي الراتب</th><th class="p-3">طباعة</th></tr></thead>
                    <tbody>${payrollRows}</tbody>
                </table>
            </div>
         </div>`;
        showModal(content);
    }
  </script>
</body>
</html>
